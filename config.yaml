- name: "example_1_single_rotated"
  background: "background.jpeg"
  output: "output/class_example_01_single_rotated.png"
  elements:
    - type: "image"
      image: "screenshot1.png"
      relative_width: 0.80
      relative_position: [0.5, 0.55]
      frame_style: "black_border"
      relative_border_width: 0.02
      relative_corner_radius: 0.2
      rotation_degrees: -7.5
      z_order: 0
- name: "example_2_double_frosted_slight_rot"
  background: "background.jpeg"
  output: "output/class_example_02_double_frosted_slight_rot.png"
  elements:
    - type: "image"
      image: "screenshot1.png"
      relative_width: 0.45
      relative_position: [0.28, 0.5]
      frame_style: "frosted_glass"
      relative_border_width: 0.04
      relative_corner_radius: 0.2
      rotation_degrees: 5
      z_order: 0
    - type: "image"
      image: "screenshot2.png"
      relative_width: 0.45
      relative_position: [0.72, 0.5]
      frame_style: "frosted_glass"
      relative_border_width: 0.04
      relative_corner_radius: 0.2
      rotation_degrees: -3
      z_order: 1
- name: "example_3_triple_shadow_angled"
  background: "background.jpeg"
  output: "output/class_example_03_triple_shadow_angled.png"
  elements:
    - type: "image"
      image: "screenshot1.png"
      relative_width: 0.50
      relative_position: [0.38, 0.5]
      frame_style: "shadow"
      relative_border_width: 0.05
      relative_corner_radius: 0.2
      rotation_degrees: -12
      z_order: 0
    - type: "image"
      image: "screenshot2.png"
      relative_width: 0.50
      relative_position: [0.5, 0.55]
      frame_style: "shadow"
      relative_border_width: 0.05
      relative_corner_radius: 0.2
      rotation_degrees: 0
      z_order: 1
    - type: "image"
      image: "screenshot3.png"
      relative_width: 0.50
      relative_position: [0.62, 0.6]
      frame_style: "shadow"
      relative_border_width: 0.05
      relative_corner_radius: 0.2
      rotation_degrees: 12
      z_order: 2
- name: "example_4_no_border_rotated"
  background: "background.jpeg"
  output: "output/class_example_04_no_border_rotated.png"
  elements:
    - type: "image"
      image: "screenshot1.png"
      relative_width: 0.6
      relative_position: [0.5, 0.5]
      frame_style: "none"
      relative_corner_radius: 0.2
      rotation_degrees: 15
      z_order: 0
- name: "example_5_mixed_styles_text"
  background: "background.jpeg"
  output: "output/class_example_05_mixed_styles_text.png"
  elements:
    - type: "image"
      image: "screenshot1.png"
      relative_width: 0.45
      relative_position: [0.3, 0.65]
      frame_style: "shadow"
      relative_border_width: 0.06
      relative_corner_radius: 0.2
      z_order: 0
      rotation_degrees: -5
    - type: "image"
      image: "screenshot2.png"
      relative_width: 0.55
      relative_position: [0.5, 0.5]
      frame_style: "frosted_glass"
      relative_border_width: 0.03
      relative_corner_radius: 0.2
      z_order: 1
      rotation_degrees: 2
    - type: "image"
      image: "screenshot3.png"
      relative_width: 0.25
      relative_position: [0.75, 0.35]
      frame_style: "none"
      relative_corner_radius: 0.2
      z_order: 3
      rotation_degrees: 10
    - type: "text"
      text: "Your Awesome App!"
      relative_position: [0.5, 0.15]
      # --- Font Selection ---
      # Use font_name to find a system font. The script will try to find the best match.
      # Use font_weight for specific styles (e.g., "Bold", "Light", "Italic").
      # This works best with `fc-match` on Linux/macOS. Fallback is filename search.
      font_name: "Helvetica Neue"
      font_weight: "Bold"
      font_size: 90
      # Alternatively, provide a direct path to a font file.
      # font_path: "/System/Library/Fonts/HelveticaNeue.ttc"
      color: [255, 255, 255, 230]
      rotation_degrees: -3
      z_order: 2
    - type: "text"
      text: "Feature Highlight"
      relative_position: [0.75, 0.85]
      font_name: "SF Pro"
      font_weight: "Regular"
      font_size: 60
      color: [50, 50, 50, 200]
      rotation_degrees: 0
      z_order: 4
- name: "example_6_text_only"
  background: "background.jpeg"
  output: "output/class_example_06_text_only.png"
  elements:
    - type: "text"
      text: "Simple. Elegant."
      relative_position: [0.5, 0.4]
      font_size: 120
      color: [20, 20, 20, 240]
      rotation_degrees: 0
      z_order: 0
    - type: "text"
      text: "Powerful."
      relative_position: [0.5, 0.6]
      font_size: 120
      color: [20, 20, 20, 240]
      rotation_degrees: 0
      z_order: 1

- name: "example_7_solid_color_bg"
  background:
    size: [1284, 2961]
    color: "#FFFFFF"
  output: "output/class_example_07_solid_color_bg.png"
  elements:
    - type: "text"
      text: "Meet                 ,"
      relative_position: [0.5, 0.06]
      font_size: 100
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "MarkFlow"
      relative_position: [0.591, 0.057]
      font_size: 100
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "AI Format Converter"
      relative_position: [0.5, 0.095]
      font_size: 100
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "screenshot1.png"
      relative_width: 0.85
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.2
      z_order: 0

- name: "example_8_gradient_bg"
  background:
    size: [1284, 2961]
    gradient:
      direction: "vertical"
      colors: ["#6a11cb", "#2575fc"]
  output: "output/class_example_08_gradient_bg.png"
  elements:
    - type: "text"
      text: "Gradient Background"
      relative_position: [0.5, 0.06]
      font_size: 100
      color: [255, 255, 255, 220]
      z_order: 1
    - type: "image"
      image: "screenshot2.png"
      relative_width: 0.85
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.2
      z_order: 0

- name: "example_9_gradient_text_test"
  background:
    size: [1284, 2961]
    color: "#FFFFFF"
  output: "output/class_example_09_gradient_text_test.png"
  elements:
    - type: "text"
      text: "Radial Gradient"
      relative_position: [0.5, 0.15]
      font_size: 80
      font_name: "Helvetica Neue"
      font_weight: "Bold"
      gradient:
        direction: "radial"
        colors: ["#FF6B6B", "#4ECDC4"]
      z_order: 1
    - type: "text"
      text: "Vertical Gradient"
      relative_position: [0.5, 0.35]
      font_size: 80
      font_name: "Helvetica Neue"
      font_weight: "Bold"
      gradient:
        direction: "vertical"
        colors: ["#A8E6CF", "#3D5A80"]
      z_order: 2
    - type: "text"
      text: "Horizontal Gradient"
      relative_position: [0.5, 0.55]
      font_size: 80
      font_name: "Helvetica Neue"
      font_weight: "Bold"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FF6B6B"]
      z_order: 3
    - type: "text"
      text: "Solid Color (Original)"
      relative_position: [0.5, 0.75]
      font_size: 80
      font_name: "Helvetica Neue"
      font_weight: "Bold"
      color: [50, 50, 50]
      z_order: 4