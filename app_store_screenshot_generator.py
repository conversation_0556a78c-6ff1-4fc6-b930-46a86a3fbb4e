#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
App Store Screenshot Generator (Class-Based)
===========================================

This script creates app store-style screenshots by placing framed device mockups
(or shadowed/borderless screenshots) containing your app screenshots, along with
optional text elements, onto a background image using relative positioning,
sizing, and rotation.

Refactored into a class-based structure for better organization.

See README.md for usage instructions.
"""
import os
import math
from PIL import Image, ImageDraw, ImageFilter, ImageChops, ImageFont, ImageColor
from typing import List, Dict, Tuple, Union, Optional, Any
import sys # For exit
import yaml
import argparse
import subprocess

# --- Constants ---
OUTPUT_DIR = "output"

# Element Types (Used in configuration dictionaries)
TYPE_IMAGE = 'image'
TYPE_TEXT = 'text'

# Style Identifiers (Used in configuration dictionaries)
STYLE_BLACK_BORDER = 'black_border'
STYLE_FROSTED_GLASS = 'frosted_glass'
STYLE_SHADOW = 'shadow'
STYLE_NONE = 'none'
VALID_FRAME_STYLES = [STYLE_BLACK_BORDER, STYLE_FROSTED_GLASS, STYLE_SHADOW, STYLE_NONE]

class ScreenshotGenerator:
    """
    Generates app store-style screenshots by composing elements onto a background.

    Attributes:
        DEFAULT_RELATIVE_BORDER_WIDTH (float): Default border width relative to element width.
        DEFAULT_RELATIVE_CORNER_RADIUS (float): Default corner radius relative to element width.
        DEFAULT_FRAME_STYLE (str): Default style for image frames.
        DEFAULT_ROTATION (float): Default rotation in degrees.
        DEFAULT_FRAME_COLOR (tuple): Default RGBA color for black border frames.
        DEFAULT_FROSTED_OVERLAY_COLOR (tuple): Default RGBA color overlay for frosted glass.
        DEFAULT_SHADOW_COLOR (tuple): Default RGBA color for shadows.
        DEFAULT_BACKGROUND_BLUR_RADIUS (int): Pixel radius for frosted glass background blur.
        DEFAULT_TEXT_COLOR (tuple): Default RGBA color for text.
    """
    # --- Class Defaults ---
    DEFAULT_RELATIVE_BORDER_WIDTH = 0.02
    DEFAULT_RELATIVE_CORNER_RADIUS = 0.2 # iPhone-like radius
    DEFAULT_FRAME_STYLE = STYLE_BLACK_BORDER
    DEFAULT_ROTATION = 0.0 # Degrees

    DEFAULT_FRAME_COLOR = (0, 0, 0, 255) # Black
    DEFAULT_FROSTED_OVERLAY_COLOR = (255, 255, 255, 45) # Very subtle light overlay
    DEFAULT_SHADOW_COLOR = (0, 0, 0, 100) # Semi-transparent black for shadow
    DEFAULT_BACKGROUND_BLUR_RADIUS = 15 # Pixel radius for frosted glass blur
    DEFAULT_TEXT_COLOR = (0, 0, 0, 255) # Black text

    def __init__(self,
                 background_source: Optional[Union[str, Image.Image]] = None,
                 size: Optional[Tuple[int, int]] = None,
                 color: Optional[Union[str, Tuple[int, int, int], Tuple[int, int, int, int]]] = None,
                 gradient: Optional[Dict[str, Any]] = None):
        """
        Initializes the ScreenshotGenerator with a background.

        The background can be loaded from an image file or generated on the fly.

        Args:
            background_source: Path to the background image file or a PIL Image object.
                               If None, a background will be generated.
            size: Required if generating a background. A tuple (width, height).
            color: Used if generating a solid color background. Can be a color name
                   (e.g., "white") or an RGB/RGBA tuple.
            gradient: Used if generating a gradient background. A dictionary with keys:
                      'direction' (e.g., 'vertical', 'horizontal') and
                      'colors' (a list of two RGB/RGBA tuples or color names).

        Raises:
            ValueError: If configuration is invalid (e.g., no source and no size).
        """
        if background_source:
            self.background_img = self._load_image(background_source, "Background")
            if not self.background_img:
                raise ValueError("Failed to load the background image from source.")
        else: # Generate background
            if not size or not isinstance(size, (list, tuple)) or len(size) != 2:
                raise ValueError("A valid 'size' (width, height) is required to generate a background.")

            # Ensure size components are integers
            size = (int(size[0]), int(size[1]))

            if gradient and isinstance(gradient, dict):
                self.background_img = self._create_gradient_background(
                    size,
                    gradient.get('direction', 'vertical'),
                    gradient.get('colors')
                )
            elif color:
                self.background_img = Image.new("RGBA", size, color)
            else: # Default to white if no color/gradient specified
                self.background_img = Image.new("RGBA", size, "white")

            if not self.background_img:
                 raise ValueError("Failed to generate the background.")

        self.bg_width, self.bg_height = self.background_img.size
        self.final_image: Optional[Image.Image] = None # Will hold the composite image

    # --- Static Utility Methods ---

    @staticmethod
    def _ensure_output_dir(path: str):
        """Creates the directory for the output path if it doesn't exist."""
        output_dir_for_file = os.path.dirname(path)
        if output_dir_for_file and not os.path.exists(output_dir_for_file):
            os.makedirs(output_dir_for_file)

    @staticmethod
    def _load_image(source: Union[str, Image.Image], source_desc: str = "Image") -> Optional[Image.Image]:
        """Loads an image from path or PIL object, converts to RGBA, handles errors."""
        if isinstance(source, str):
            try:
                img = Image.open(source).convert("RGBA")
                # print(f"Debug: Loaded image '{source}' ({img.size[0]}x{img.size[1]})") # Optional debug
                return img
            except FileNotFoundError:
                print(f"Error: {source_desc} file not found at {source}")
                return None
            except Exception as e:
                print(f"Error opening {source_desc} file {source}: {e}")
                return None
        elif isinstance(source, Image.Image):
            # print(f"Debug: Using provided PIL Image object ({source.size[0]}x{source.size[1]})") # Optional debug
            return source.copy().convert("RGBA")
        else:
            print(f"Error: Invalid {source_desc} type. Must be path string or PIL Image.")
            return None

    @staticmethod
    def _load_font(
        font_path: Optional[str],
        font_size: Optional[int],
        element_desc: str,
        font_index: Optional[int] = 0, # Default to 0 for non-collection files
        font_weight: Optional[str] = None # Keep for logging, but not for variation
    ) -> Optional[ImageFont.FreeTypeFont]:
        """
        Loads a font using ImageFont, with support for font collections (.ttc).
        """
        if not font_path:
            print(f"Error: Missing font path for {element_desc}. Could not find a suitable font.")
            return None
        if not font_size or font_size <= 0:
            print(f"Error: Missing or invalid 'font_size' for {element_desc}.")
            return None

        try:
            # Use the index parameter for .ttc files
            font = ImageFont.truetype(font_path, font_size, index=font_index or 0)
            return font
        except IOError:
            print(f"Error: Font file not found or cannot be read at '{font_path}' for {element_desc}.")
            return None
        except Exception as e:
            # Provide more specific error feedback if an index is used
            index_info = f" with index {font_index}" if font_index is not None else ""
            print(f"Error loading font '{font_path}'{index_info} for {element_desc}: {e}")
            return None

    @staticmethod
    def _create_rounded_mask(size: Tuple[int, int], radius: int) -> Image.Image:
        """Creates a grayscale mask (L mode) for a rounded rectangle."""
        if radius <= 0: # Use a simple rectangular mask if no radius
            return Image.new("L", size, 255) # Full white mask

        mask = Image.new("L", size, 0) # Black background (transparent area)
        draw = ImageDraw.Draw(mask)
        draw.rounded_rectangle(
            [(0, 0), (size[0] - 1, size[1] - 1)],
            radius=radius,
            fill=255 # White foreground (opaque area)
        )
        return mask

    @staticmethod
    def _create_gradient_background(size: Tuple[int, int], direction: str, colors: List) -> Optional[Image.Image]:
        """Creates a gradient background image."""
        if not colors or len(colors) < 2:
            print("Error: Gradient requires at least two colors.")
            return None

        width, height = size
        img = Image.new("RGBA", (width, height), "#FFFFFF")
        draw = ImageDraw.Draw(img)

        try:
            # Convert color names to RGBA tuples if needed
            c1 = ImageColor.getcolor(colors[0], "RGBA")
            c2 = ImageColor.getcolor(colors[1], "RGBA")
        except (ValueError, AttributeError) as e:
            print(f"Error parsing gradient color: {e}")
            return None

        direction = direction.lower()
        if direction == 'vertical':
            for y in range(height):
                r = int(c1[0] + (c2[0] - c1[0]) * y / height)
                g = int(c1[1] + (c2[1] - c1[1]) * y / height)
                b = int(c1[2] + (c2[2] - c1[2]) * y / height)
                a = int(c1[3] + (c2[3] - c1[3]) * y / height)
                draw.line([(0, y), (width, y)], fill=(r, g, b, a))
        elif direction == 'horizontal':
            for x in range(width):
                r = int(c1[0] + (c2[0] - c1[0]) * x / width)
                g = int(c1[1] + (c2[1] - c1[1]) * x / width)
                b = int(c1[2] + (c2[2] - c1[2]) * x / width)
                a = int(c1[3] + (c2[3] - c1[3]) * x / width)
                draw.line([(x, 0), (x, height)], fill=(r, g, b, a))
        elif direction == 'radial':
            # Radial gradient from center to edges
            center_x, center_y = width // 2, height // 2
            max_distance = math.sqrt((width // 2) ** 2 + (height // 2) ** 2)

            for y in range(height):
                for x in range(width):
                    distance = math.sqrt((x - center_x) ** 2 + (y - center_y) ** 2)
                    ratio = min(distance / max_distance, 1.0)

                    r = int(c1[0] + (c2[0] - c1[0]) * ratio)
                    g = int(c1[1] + (c2[1] - c1[1]) * ratio)
                    b = int(c1[2] + (c2[2] - c1[2]) * ratio)
                    a = int(c1[3] + (c2[3] - c1[3]) * ratio)

                    draw.point((x, y), fill=(r, g, b, a))
        else:
            print(f"Warning: Unsupported gradient direction '{direction}'. Defaulting to 'vertical'.")
            for y in range(height):
                r = int(c1[0] + (c2[0] - c1[0]) * y / height)
                g = int(c1[1] + (c2[1] - c1[1]) * y / height)
                b = int(c1[2] + (c2[2] - c1[2]) * y / height)
                a = int(c1[3] + (c2[3] - c1[3]) * y / height)
                draw.line([(0, y), (width, y)], fill=(r, g, b, a))

        return img

    @staticmethod
    def _create_text_gradient(
        text_img: Image.Image,
        direction: str,
        colors: List,
        text_bounds: Tuple[int, int, int, int]
    ) -> Optional[Image.Image]:
        """
        Creates a gradient effect for text.

        Args:
            text_img: The text image with alpha channel
            direction: Gradient direction ('vertical', 'horizontal', 'radial')
            colors: List of colors for the gradient
            text_bounds: Bounding box of the actual text (x1, y1, x2, y2)

        Returns:
            Image with gradient applied to text, or None if failed
        """
        if not colors or len(colors) < 2:
            print("Error: Text gradient requires at least two colors.")
            return None

        try:
            # Convert color names to RGBA tuples if needed
            c1 = ImageColor.getcolor(colors[0], "RGBA")
            c2 = ImageColor.getcolor(colors[1], "RGBA")
        except (ValueError, AttributeError) as e:
            print(f"Error parsing text gradient color: {e}")
            return None

        width, height = text_img.size
        gradient_img = Image.new("RGBA", (width, height), (0, 0, 0, 0))

        # Extract text bounds for gradient calculation
        text_x1, text_y1, text_x2, text_y2 = text_bounds
        text_width = text_x2 - text_x1
        text_height = text_y2 - text_y1
        text_center_x = text_x1 + text_width // 2
        text_center_y = text_y1 + text_height // 2

        direction = direction.lower()

        # Create gradient based on direction
        for y in range(height):
            for x in range(width):
                # Only process pixels that have text (non-zero alpha)
                if text_img.getpixel((x, y))[3] > 0:
                    if direction == 'vertical':
                        if text_height > 0:
                            ratio = (y - text_y1) / text_height
                            ratio = max(0, min(1, ratio))
                        else:
                            ratio = 0
                    elif direction == 'horizontal':
                        if text_width > 0:
                            ratio = (x - text_x1) / text_width
                            ratio = max(0, min(1, ratio))
                        else:
                            ratio = 0
                    elif direction == 'radial':
                        # Calculate distance from text center
                        dx = x - text_center_x
                        dy = y - text_center_y
                        distance = math.sqrt(dx * dx + dy * dy)
                        max_distance = math.sqrt((text_width // 2) ** 2 + (text_height // 2) ** 2)
                        if max_distance > 0:
                            ratio = min(distance / max_distance, 1.0)
                        else:
                            ratio = 0
                    else:
                        ratio = 0

                    # Interpolate colors
                    r = int(c1[0] + (c2[0] - c1[0]) * ratio)
                    g = int(c1[1] + (c2[1] - c1[1]) * ratio)
                    b = int(c1[2] + (c2[2] - c1[2]) * ratio)
                    a = int(c1[3] + (c2[3] - c1[3]) * ratio)

                    # Use original text alpha
                    original_alpha = text_img.getpixel((x, y))[3]
                    final_alpha = int(a * original_alpha / 255)

                    gradient_img.putpixel((x, y), (r, g, b, final_alpha))

        return gradient_img

    @staticmethod
    def _prepare_screenshot_content(
        screenshot_img: Image.Image,
        content_size: Tuple[int, int],
        corner_radius: int
    ) -> Optional[Image.Image]:
        """Resizes screenshot and applies rounded corners mask."""
        try:
            content_w, content_h = content_size
            if content_w <= 0 or content_h <= 0:
                print(f"Error: Invalid content resize dimensions: {content_w}x{content_h}")
                return None
            resized_ss = screenshot_img.resize(content_size, Image.Resampling.LANCZOS)

            if corner_radius > 0:
                mask = ScreenshotGenerator._create_rounded_mask(content_size, corner_radius)
                resized_ss.putalpha(mask) # Apply mask to alpha channel
            return resized_ss
        except Exception as e:
            print(f"Error preparing screenshot content (resizing/masking): {e}")
            return None

    @staticmethod
    def _create_bordered_frame(
        size: Tuple[int, int],
        content_size: Tuple[int, int],
        border_width: int,
        corner_radius: int,
        color: Tuple[int, int, int, int]
    ) -> Optional[Image.Image]:
        """Creates a simple frame with a solid color border."""
        if border_width <= 0: return None
        width, height = size
        content_width, content_height = content_size
        if width <=0 or height <=0: return None

        frame = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(frame)
        content_x = (width - content_width) // 2
        content_y = (height - content_height) // 2

        draw.rounded_rectangle([(0, 0), (width - 1, height - 1)], radius=corner_radius, fill=color)

        inner_corner_radius = max(0, corner_radius - border_width)
        inner_rect = [(content_x, content_y), (content_x + content_width - 1, content_y + content_height - 1)]

        if content_width > 0 and content_height > 0:
            draw.rounded_rectangle(inner_rect, radius=inner_corner_radius, fill=(0, 0, 0, 0))
        return frame

    @staticmethod
    def _create_shadow_effect(
        content_size: Tuple[int, int],
        corner_radius: int,
        shadow_color: Tuple[int, int, int, int],
        blur_radius: int
    ) -> Tuple[Optional[Image.Image], Tuple[int, int]]:
        """Creates a blurred shadow layer for the given content shape."""
        if blur_radius <= 0: return None, (0, 0)
        content_w, content_h = content_size
        if content_w <= 0 or content_h <= 0: return None, (0, 0)

        padding = math.ceil(blur_radius * 3)
        shadow_canvas_w = content_w + padding * 2
        shadow_canvas_h = content_h + padding * 2

        shadow_layer = Image.new('RGBA', (shadow_canvas_w, shadow_canvas_h), (0, 0, 0, 0))
        draw = ImageDraw.Draw(shadow_layer)
        shape_rect = [(padding, padding), (padding + content_w - 1, padding + content_h - 1)]
        draw.rounded_rectangle(shape_rect, radius=corner_radius, fill=shadow_color)

        blurred_shadow = shadow_layer.filter(ImageFilter.GaussianBlur(radius=blur_radius))
        paste_offset_x = -padding
        paste_offset_y = -padding

        return blurred_shadow, (paste_offset_x, paste_offset_y)

    @staticmethod
    def _rotate_element(element: Image.Image, angle: float, origin_center: Tuple[int, int]) -> Tuple[Image.Image, Tuple[int, int]]:
        """Rotates an element (Image object) around its original intended center on the final canvas."""
        if angle == 0:
            orig_w, orig_h = element.size
            paste_x = origin_center[0] - orig_w // 2
            paste_y = origin_center[1] - orig_h // 2
            return element, (paste_x, paste_y)

        # Rotate the element itself
        rotated_element = element.rotate(angle, resample=Image.Resampling.BICUBIC, expand=True, fillcolor=(0, 0, 0, 0))
        rot_w, rot_h = rotated_element.size

        # The paste position is the desired center minus half the *new* rotated dimensions
        paste_x = origin_center[0] - rot_w // 2
        paste_y = origin_center[1] - rot_h // 2
        return rotated_element, (paste_x, paste_y)

    # --- Instance Methods for Processing ---

    def _calculate_image_geometry(
        self,
        ss_aspect_ratio: float,
        config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Calculates absolute pixel dimensions and positions for IMAGE elements."""
        bg_width, bg_height = self.bg_width, self.bg_height
        rel_width = config['relative_width'] # Required
        rel_center_pos = config['relative_position'] # Required

        style = config.get('frame_style', self.DEFAULT_FRAME_STYLE).lower()
        if style not in VALID_FRAME_STYLES:
            print(f"Warning: Invalid frame_style '{style}'. Using default '{self.DEFAULT_FRAME_STYLE}'.")
            style = self.DEFAULT_FRAME_STYLE

        rel_border = config.get('relative_border_width', self.DEFAULT_RELATIVE_BORDER_WIDTH)
        rel_radius = config.get('relative_corner_radius', self.DEFAULT_RELATIVE_CORNER_RADIUS)

        if style == STYLE_FROSTED_GLASS:
            base_color = config.get('frame_color', self.DEFAULT_FROSTED_OVERLAY_COLOR)
        elif style == STYLE_SHADOW:
            base_color = config.get('frame_color', self.DEFAULT_SHADOW_COLOR)
        else: # black_border, none
            base_color = config.get('frame_color', self.DEFAULT_FRAME_COLOR)

        base_width = int(bg_width * rel_width)
        if base_width <= 0:
            print(f"Error: Calculated base width ({base_width}) is zero or negative. Check 'relative_width'.")
            return None
        border_abs = max(0, int(base_width * rel_border))
        radius_abs = max(0, int(base_width * rel_radius))

        element_w, element_h = 0, 0
        content_w, content_h = 0, 0
        shadow_blur_radius = 0

        # Calculate dimensions based on style
        if style == STYLE_SHADOW:
            content_w = base_width
            content_h = int(content_w * ss_aspect_ratio) if ss_aspect_ratio > 0 else 0
            element_w, element_h = content_w, content_h # Shadow element size *is* content size for positioning
            shadow_blur_radius = max(1, border_abs) if border_abs > 0 else 0 # Use border width for blur intensity
            border_abs = 0 # No visible border for shadow style

        elif style in [STYLE_BLACK_BORDER, STYLE_FROSTED_GLASS, STYLE_NONE]:
            if style == STYLE_NONE: border_abs = 0

            # Start with element width based on relative_width
            element_w = base_width
            # Estimate element height based on aspect ratio and border
            temp_element_h_est = int(element_w * ss_aspect_ratio) + 2 * border_abs if ss_aspect_ratio > 0 else 2 * border_abs
            if temp_element_h_est <= 0: temp_element_h_est = 1

            # Ensure border and radius don't exceed half the dimensions
            safe_border = min(border_abs, element_w // 2 -1, temp_element_h_est // 2 -1)
            safe_border = max(0, safe_border) # Ensure non-negative

            # Recalculate content size based on safe border
            temp_content_w = max(1, element_w - 2 * safe_border)
            temp_content_h = max(1, int(temp_content_w * ss_aspect_ratio)) if ss_aspect_ratio > 0 else 1

            # Ensure radius doesn't exceed half the *content* dimensions
            safe_radius = min(radius_abs, temp_content_w // 2, temp_content_h // 2)
            safe_radius = max(0, safe_radius)

            # Final dimensions using safe values
            border_abs = safe_border
            radius_abs = safe_radius
            content_w = element_w - 2 * border_abs
            if content_w <= 0:
                print(f"Error: Calculated content width ({content_w}) not positive.")
                return None
            content_h = int(content_w * ss_aspect_ratio) if ss_aspect_ratio > 0 else 0
            if content_h <= 0:
                 print(f"Error: Calculated content height ({content_h}) not positive.")
                 return None
            element_h = content_h + 2 * border_abs
            if element_h <= 0:
                print(f"Error: Calculated element height ({element_h}) not positive.")
                return None
        else: # Should be caught earlier, but defensively:
             print(f"Error: Unknown frame_style '{style}'.")
             return None

        # Calculate final center and paste coordinates
        center_x = int(bg_width * rel_center_pos[0])
        center_y = int(bg_height * rel_center_pos[1])
        # Top-left corner for the element (including border/shadow padding)
        # Note: For shadow, element_w/h is content size, shadow is drawn outside this.
        # For border/frosted, element_w/h includes the border.
        paste_x = center_x - element_w // 2
        paste_y = center_y - element_h // 2
        # Top-left corner for the *content* within the element area
        content_paste_x = paste_x + border_abs
        content_paste_y = paste_y + border_abs

        return {
            'element_w': element_w, 'element_h': element_h,
            'content_w': content_w, 'content_h': content_h,
            'border_abs': border_abs, 'radius_abs': radius_abs,
            'center_x': center_x, 'center_y': center_y,
            'paste_x': paste_x, 'paste_y': paste_y, # Top-left of element bounding box
            'content_paste_x': content_paste_x, 'content_paste_y': content_paste_y, # Top-left of content area
            'style': style, 'color': base_color,
            'shadow_blur_radius': shadow_blur_radius
        }

    def _create_frosted_glass_patch(
        self,
        frame_geom: Dict[str, Any],
        background_to_sample: Image.Image
    ) -> Optional[Image.Image]:
        """
        Creates the frosted glass effect as a separate, unrotated image patch.

        Args:
            frame_geom: Geometry dictionary calculated by _calculate_image_geometry.
            background_to_sample: The background image (or current final_image state)
                                  from which to sample the area to blur.

        Returns:
            A PIL Image object containing the blurred, masked, and overlaid
            frosted glass patch, or None if creation fails.
        """
        border_width = frame_geom['border_abs']
        if border_width <= 0:
            # print("Debug: No border width for frosted glass, skipping patch.")
            return None # No effect if no border

        paste_x, paste_y = frame_geom['paste_x'], frame_geom['paste_y']
        element_w, element_h = frame_geom['element_w'], frame_geom['element_h']
        content_w, content_h = frame_geom['content_w'], frame_geom['content_h']
        radius_abs = frame_geom['radius_abs']
        overlay_color = frame_geom['color']
        blur_radius = self.DEFAULT_BACKGROUND_BLUR_RADIUS

        # Crop the area from the background corresponding to the element's bounding box
        crop_box = (max(0, paste_x), max(0, paste_y),
                    min(self.bg_width, paste_x + element_w), min(self.bg_height, paste_y + element_h))

        if crop_box[2] <= crop_box[0] or crop_box[3] <= crop_box[1]:
            print("Warning: Frosted glass crop area is invalid.")
            return None

        try:
            bg_crop = background_to_sample.crop(crop_box)
            blurred_bg = bg_crop.filter(ImageFilter.GaussianBlur(radius=blur_radius))
        except Exception as e:
            print(f"Error cropping/blurring for frosted glass: {e}")
            return None

        # Create a mask for the frame shape (border area only)
        patch_size = blurred_bg.size
        frame_mask = Image.new('L', patch_size, 0) # Start fully transparent
        draw = ImageDraw.Draw(frame_mask)

        # Draw outer rounded rectangle (opaque)
        effective_radius_outer = min(radius_abs, patch_size[0]//2, patch_size[1]//2)
        draw.rounded_rectangle([(0, 0), (patch_size[0] - 1, patch_size[1] - 1)], radius=effective_radius_outer, fill=255)

        # Calculate inner rectangle position relative to the *patch* (cropped area)
        # Need to account for potential cropping if element was near edge
        inner_x1_rel = frame_geom['content_paste_x'] - crop_box[0]
        inner_y1_rel = frame_geom['content_paste_y'] - crop_box[1]
        inner_x2_rel = inner_x1_rel + content_w - 1
        inner_y2_rel = inner_y1_rel + content_h - 1

        # Draw inner rounded rectangle (transparent) to cut out the center
        if content_w > 0 and content_h > 0 and inner_x1_rel < inner_x2_rel and inner_y1_rel < inner_y2_rel:
            inner_corner_radius = max(0, radius_abs - border_width)
            effective_radius_inner = min(inner_corner_radius, content_w//2, content_h//2)
            # Clamp inner rectangle coordinates to be within patch boundaries
            inner_x1_clamped = max(0, inner_x1_rel)
            inner_y1_clamped = max(0, inner_y1_rel)
            inner_x2_clamped = min(patch_size[0]-1, inner_x2_rel)
            inner_y2_clamped = min(patch_size[1]-1, inner_y2_rel)
            if inner_x1_clamped < inner_x2_clamped and inner_y1_clamped < inner_y2_clamped:
                 draw.rounded_rectangle(
                     [(inner_x1_clamped, inner_y1_clamped), (inner_x2_clamped, inner_y2_clamped)],
                     radius=effective_radius_inner, fill=0 # Make center transparent
                 )
            else:
                 print("Warning: Inner rectangle for frosted mask became invalid after clamping.")
                 # Draw full opaque mask if inner cut fails? Or return None?
                 # For now, proceed with the outer mask only.

        # Apply overlay color if specified
        if overlay_color and overlay_color[3] > 0:
            overlay = Image.new('RGBA', blurred_bg.size, overlay_color)
            # Composite overlay onto the blurred background first
            final_patch = Image.alpha_composite(blurred_bg.convert("RGBA"), overlay)
        else:
            final_patch = blurred_bg.convert("RGBA")

        # Apply the frame mask to the final patch's alpha channel
        final_patch.putalpha(frame_mask)

        return final_patch


    def _process_image_element(self, config: Dict[str, Any], element_id_str: str):
        """Loads, processes, and pastes a single image element onto final_image."""
        if not self.final_image: return False # Should not happen if called from generate

        # --- Validate config ---
        if not config.get('image'):
            print(f"Warning: Skipping {element_id_str} due to missing 'image' key.")
            return False
        if config.get('relative_width') is None:
            print(f"Warning: Skipping {element_id_str} due to missing 'relative_width' key.")
            return False

        screenshot_img = self._load_image(config['image'], f"Screenshot for {element_id_str}")
        if not screenshot_img: return False
        ss_w_orig, ss_h_orig = screenshot_img.size
        if ss_w_orig <= 0 or ss_h_orig <= 0:
            print(f"Warning: Screenshot for {element_id_str} has invalid dimensions ({ss_w_orig}x{ss_h_orig}). Skipping.")
            return False
        aspect_ratio = ss_h_orig / ss_w_orig if ss_w_orig > 0 else 0 # Avoid division by zero

        # --- Calculate Geometry ---
        try:
            geom = self._calculate_image_geometry(aspect_ratio, config)
            if not geom:
                print(f"Warning: Failed to calculate geometry for {element_id_str}. Skipping.")
                return False
        except Exception as e:
            print(f"Error calculating geometry for {element_id_str}: {e}")
            return False

        # --- Prepare Content Image (Unrotated) ---
        # Radius for content: If shadow or no border, use main radius. If border, use radius minus border.
        content_radius = geom['radius_abs'] if geom['style'] == STYLE_SHADOW or geom['border_abs'] == 0 else max(0, geom['radius_abs'] - geom['border_abs'])
        content_img_unrot = self._prepare_screenshot_content(screenshot_img, (geom['content_w'], geom['content_h']), content_radius)
        if not content_img_unrot:
            print(f"Warning: Failed to prepare screenshot content for {element_id_str}. Skipping.")
            return False

        # --- Prepare Effects/Frames (Unrotated) ---
        shadow_layer_unrot, shadow_offset_unrot = None, (0, 0)
        if geom['style'] == STYLE_SHADOW and geom['shadow_blur_radius'] > 0:
            shadow_layer_unrot, shadow_offset_unrot = self._create_shadow_effect(
                (geom['content_w'], geom['content_h']), geom['radius_abs'], geom['color'], geom['shadow_blur_radius']
            )

        border_frame_unrot = None
        if geom['style'] == STYLE_BLACK_BORDER and geom['border_abs'] > 0:
            border_frame_unrot = self._create_bordered_frame(
                (geom['element_w'], geom['element_h']), (geom['content_w'], geom['content_h']),
                geom['border_abs'], geom['radius_abs'], geom['color']
            )

        frosted_patch_unrot = None
        if geom['style'] == STYLE_FROSTED_GLASS and geom['border_abs'] > 0:
             # Pass self.final_image because frosting needs to sample the *current* state
             frosted_patch_unrot = self._create_frosted_glass_patch(geom, self.final_image)
             if not frosted_patch_unrot:
                 print(f"Warning: Failed to create frosted glass patch for {element_id_str}.")
                 # Proceed without frost if creation failed


        # --- Rotate and Paste Layers ---
        rotation_degrees = config.get('rotation_degrees', self.DEFAULT_ROTATION)
        # The origin for rotation is the calculated center point on the final canvas
        origin_center = (geom['center_x'], geom['center_y'])

        # Layer Order: Shadow -> Frosted -> Content -> Border

        # 1. Paste Shadow (if applicable)
        if shadow_layer_unrot:
            # Shadow layer's origin needs careful calculation relative to content center
            # Shadow offset is relative to top-left of content
            unrot_shadow_center_x = geom['content_paste_x'] + shadow_offset_unrot[0] + shadow_layer_unrot.width // 2
            unrot_shadow_center_y = geom['content_paste_y'] + shadow_offset_unrot[1] + shadow_layer_unrot.height // 2
            # Rotate shadow around ITS calculated center
            rotated_shadow, (rot_shadow_paste_x, rot_shadow_paste_y) = self._rotate_element(
                shadow_layer_unrot, rotation_degrees, (unrot_shadow_center_x, unrot_shadow_center_y)
            )
            self.final_image.paste(rotated_shadow, (rot_shadow_paste_x, rot_shadow_paste_y), rotated_shadow)

        # 2. Paste Frosted Glass Patch (if applicable)
        if frosted_patch_unrot:
            # Rotate the frosted patch around the main element's center
            rotated_frost, (rot_frost_paste_x, rot_frost_paste_y) = self._rotate_element(
                frosted_patch_unrot, rotation_degrees, origin_center
            )
            self.final_image.paste(rotated_frost, (rot_frost_paste_x, rot_frost_paste_y), rotated_frost)

        # 3. Paste Content
        rotated_content, (rot_content_paste_x, rot_content_paste_y) = self._rotate_element(
            content_img_unrot, rotation_degrees, origin_center
        )
        self.final_image.paste(rotated_content, (rot_content_paste_x, rot_content_paste_y), rotated_content)

        # 4. Paste Border Frame (if applicable)
        if border_frame_unrot:
            # Rotate the border frame around the main element's center
            rotated_frame, (rot_frame_paste_x, rot_frame_paste_y) = self._rotate_element(
                border_frame_unrot, rotation_degrees, origin_center
            )
            self.final_image.paste(rotated_frame, (rot_frame_paste_x, rot_frame_paste_y), rotated_frame)

        return True # Success

    def _process_text_element(self, config: Dict[str, Any], element_id_str: str):
        """Loads, processes, and pastes a single text element onto final_image."""
        if not self.final_image: return False
        draw = ImageDraw.Draw(self.final_image) # Need draw context for text measurement

        # --- Validate config ---
        text = config.get('text')
        font_path = config.get('font_path')
        font_size = config.get('font_size')
        font_weight = config.get('font_weight') # For context
        font_index = config.get('font_index', 0) # Get the index, default to 0
        if not text:
             print(f"Warning: Skipping {element_id_str} due to missing 'text' key.")
             return False

        font = self._load_font(font_path, font_size, element_id_str, font_index, font_weight)
        if not font: return False # Error handled by load_font

        # Check for gradient or solid color
        gradient_config = config.get('gradient')
        color = tuple(config.get('color', self.DEFAULT_TEXT_COLOR))
        rotation_degrees = config.get('rotation_degrees', self.DEFAULT_ROTATION)
        rel_center_pos = config['relative_position'] # Already validated in generate()
        center_x = int(self.bg_width * rel_center_pos[0])
        center_y = int(self.bg_height * rel_center_pos[1])

        # --- Calculate Text Size & Create Temp Image ---
        try:
            # Use textbbox for accurate sizing (left, top, right, bottom)
            bbox = draw.textbbox((0, 0), text, font=font, anchor="lt") # Assume left-top anchor for measurement
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            offset_x = bbox[0] # Offset from (0,0) where drawing starts
            offset_y = bbox[1]
        except AttributeError:
            print(f"Warning: Using legacy textsize for {element_id_str} (PIL < 8.0.0?). Bbox less accurate.")
            try: # Fallback for older PIL
                 # textsize/textlength might not account for font ascender/descender fully
                if hasattr(draw, 'textlength'): text_width = draw.textlength(text, font=font)
                else: text_width, _ = draw.textsize(text, font=font)
                # Estimate height, less reliable
                _, text_height_approx = draw.textsize("Ay", font=font) # Use characters with ascender/descender
                text_height = text_height_approx
                offset_x, offset_y = 0, 0 # Assume no offset for legacy
            except Exception as e_legacy:
                 print(f"Error: Cannot determine text dimensions for {element_id_str} using legacy methods: {e_legacy}")
                 return False

        if text_width <= 0 or text_height <= 0:
             print(f"Warning: Invalid text dimensions calculated for {element_id_str} ({text_width}x{text_height}). Skipping.")
             return False

        # Add padding to temporary image to avoid clipping during rotation/drawing
        pad = math.ceil(max(text_width, text_height) * 0.1) # Add 10% padding, min 5 pixels
        pad = max(5, pad)
        txt_img_w = text_width + 2 * pad
        txt_img_h = text_height + 2 * pad

        # Position text within the padded temporary image, accounting for the bbox offset
        draw_pos_in_temp_x = pad - offset_x
        draw_pos_in_temp_y = pad - offset_y

        txt_img = Image.new('RGBA', (int(txt_img_w), int(txt_img_h)), (0,0,0,0))
        txt_draw = ImageDraw.Draw(txt_img)

        # Check if gradient is requested
        if gradient_config and isinstance(gradient_config, dict):
            # First draw text in white to create a mask
            txt_draw.text((draw_pos_in_temp_x, draw_pos_in_temp_y), text, font=font, fill=(255, 255, 255, 255), anchor="lt")

            # Calculate text bounds for gradient
            text_bounds = (
                int(draw_pos_in_temp_x),
                int(draw_pos_in_temp_y),
                int(draw_pos_in_temp_x + text_width),
                int(draw_pos_in_temp_y + text_height)
            )

            # Apply gradient
            gradient_direction = gradient_config.get('direction', 'vertical')
            gradient_colors = gradient_config.get('colors', ['#000000', '#666666'])

            gradient_text = self._create_text_gradient(
                txt_img, gradient_direction, gradient_colors, text_bounds
            )

            if gradient_text:
                txt_img = gradient_text
            else:
                print(f"Warning: Failed to create gradient for {element_id_str}, using solid color.")
                # Fallback to solid color
                txt_img = Image.new('RGBA', (int(txt_img_w), int(txt_img_h)), (0,0,0,0))
                txt_draw = ImageDraw.Draw(txt_img)
                txt_draw.text((draw_pos_in_temp_x, draw_pos_in_temp_y), text, font=font, fill=color, anchor="lt")
        else:
            # Draw text using solid color (original behavior)
            txt_draw.text((draw_pos_in_temp_x, draw_pos_in_temp_y), text, font=font, fill=color, anchor="lt")

        # --- Rotate and Paste ---
        # Rotate the temporary text image around the intended final center point
        rotated_text_img, (paste_x, paste_y) = self._rotate_element(
            txt_img,
            rotation_degrees,
            (center_x, center_y) # Rotate around the intended final center on the main canvas
        )
        self.final_image.paste(rotated_text_img, (paste_x, paste_y), rotated_text_img)

        return True # Success

    # --- Main Generation Method ---

    def generate(self, elements_config: List[Dict], output_path: Optional[str] = None) -> Optional[Image.Image]:
        """
        Creates the app store screenshot by placing configured elements onto the background.

        Args:
            elements_config: List of dictionaries, each configuring an image or text element.
                             Required keys depend on type:
                             - Common: 'relative_position', 'z_order', 'rotation_degrees' (optional)
                             - Image: 'image', 'relative_width', 'frame_style' (optional), ...
                             - Text: 'text', 'font_path', 'font_size', 'color' (optional)
            output_path: Path to save the final image. If None, returns the PIL Image.

        Returns:
            PIL Image object if output_path is None and generation succeeds.
            None if output_path is provided and save succeeds.
            The generated Image object might be returned on save error.
            None on fatal errors during setup or if no elements are processed.
        """
        # Create the final canvas by copying the background
        self.final_image = self.background_img.copy()

        # Sort configurations by z_order (lower z is drawn first)
        try:
            elements_config_sorted = sorted(
                elements_config,
                key=lambda cfg: cfg.get('z_order', 0)
            )
        except Exception as e:
            print(f"Error sorting configurations by 'z_order': {e}")
            self.final_image = None # Indicate failure
            return None

        processed_elements_count = 0
        for i, config in enumerate(elements_config_sorted):
            element_type = config.get('type', TYPE_IMAGE) # Default to image
            element_id_str = f"Element {i+1} (type={element_type}, z={config.get('z_order', 0)})"
            print(f"\nProcessing {element_id_str}...")

            # Validate common config
            rel_pos = config.get('relative_position')
            if not isinstance(rel_pos, (tuple, list)) or len(rel_pos) != 2:
                print(f"Warning: Skipping {element_id_str} due to missing/invalid 'relative_position'. Must be tuple/list of length 2.")
                continue
            try:
                rel_pos = (float(rel_pos[0]), float(rel_pos[1])) # Ensure floats
                config['relative_position'] = rel_pos # Update config with validated tuple
            except (ValueError, TypeError):
                print(f"Warning: Skipping {element_id_str} due to non-numeric 'relative_position' values.")
                continue


            success = False
            try:
                if element_type == TYPE_IMAGE:
                    success = self._process_image_element(config, element_id_str)
                elif element_type == TYPE_TEXT:
                    success = self._process_text_element(config, element_id_str)
                else:
                    print(f"Warning: Skipping {element_id_str} due to unknown type '{element_type}'.")
                    continue
            except Exception as e:
                print(f"!!! Unhandled Error processing {element_id_str}: {e} !!!")
                # Optionally: import traceback; traceback.print_exc()
                success = False # Mark as failed

            if success:
                processed_elements_count += 1
            else:
                print(f"...Processing failed for {element_id_str}.")


        if processed_elements_count == 0:
            print("\nWarning: No elements were successfully processed.")
            # Keep self.final_image as the background for potential return/save

        # --- Output ---
        result_image = self.final_image
        self.final_image = None # Clear instance variable for next potential run

        if result_image is None: # Should only happen if sorting failed
            print("Error: Final image is None before output stage.")
            return None
        
        # 转换为RGB模式，移除alpha通道
        if result_image.mode == 'RGBA':
            # 创建纯白色RGB背景
            rgb_background = Image.new('RGB', result_image.size, (255, 255, 255))
            # 将RGBA图像合成到RGB背景上，确保完全不透明
            rgb_background.paste(result_image, mask=result_image.split()[3])
            result_image = rgb_background
        elif result_image.mode != 'RGB':
            # 处理其他可能的模式（如L、P等）
            result_image = result_image.convert('RGB')

        if output_path:
            try:
                self._ensure_output_dir(output_path)
                result_image.save(output_path)
                print(f"\nScreenshot saved to: {output_path}")
                return None # Indicate success when saving
            except Exception as e:
                print(f"Error saving final image to {output_path}: {e}")
                return result_image # Return image even if save failed
        else:
            return result_image # Return the image object


def load_config(config_path: str) -> Optional[List[Dict]]:
    """Loads and parses the YAML configuration file."""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        if not isinstance(config, list):
            print(f"Error: YAML config root must be a list of definitions, but got {type(config)}.")
            return None
        return config
    except FileNotFoundError:
        print(f"Error: Configuration file not found at '{config_path}'")
        return None
    except yaml.YAMLError as e:
        print(f"Error parsing YAML file '{config_path}': {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred while loading config: {e}")
        return None

class FontManager:
    """
    Manages finding and caching system fonts.
    Tries to use `fc-match` on Linux/macOS for accurate font finding.
    Falls back to a manual search of common font directories.
    Caches results to avoid repeated searches.
    """
    def __init__(self):
        self._cache: Dict[Tuple[str, Optional[str]], Optional[str]] = {}
        self._scanned_dirs = False
        self._all_font_files: List[str] = []
        self.platform = sys.platform

    def _get_system_font_dirs(self) -> List[str]:
        """Returns a list of common font directories for the current OS."""
        if self.platform == "win32":
            win_dir = os.environ.get("SystemRoot", "C:\\Windows")
            return [os.path.join(win_dir, "Fonts")]
        elif self.platform == "darwin":
            return ["/System/Library/Fonts", "/Library/Fonts", os.path.expanduser("~/Library/Fonts")]
        else: # Linux and other Unix-likes
            return [
                "/usr/share/fonts",
                "/usr/local/share/fonts",
                os.path.expanduser("~/.fonts"),
                os.path.expanduser("~/.local/share/fonts")
            ]

    def _scan_font_files(self):
        """Scans system font directories to build a list of all font files."""
        if self._scanned_dirs:
            return
        font_dirs = self._get_system_font_dirs()
        font_files = []
        for d in font_dirs:
            if os.path.isdir(d):
                for root, _, files in os.walk(d):
                    for file in files:
                        if file.lower().endswith(('.ttf', '.otf', '.ttc')):
                            font_files.append(os.path.join(root, file))
        self._all_font_files = font_files
        self._scanned_dirs = True

    def _find_font_manual(self, name: str, weight: Optional[str]) -> Optional[str]:
        """
        Finds a font by manually searching through file names.
        This is a fallback and less accurate.
        """
        if not self._scanned_dirs:
            self._scan_font_files()

        name_lower = name.lower().replace(" ", "")
        weight_lower = weight.lower() if weight else ''

        candidates = []
        for font_path in self._all_font_files:
            file_name_lower = os.path.basename(font_path).lower()
            if name_lower in file_name_lower:
                candidates.append(font_path)

        if not candidates:
            return None

        if weight_lower:
            # Prioritize candidates with weight in filename
            for path in candidates:
                if weight_lower in os.path.basename(path).lower():
                    return path

        # If no weight match, return the first candidate
        return candidates[0]

    def _get_font_index_from_file(self, font_path: str, weight: str) -> int:
        """Tries to find the correct font index for a given weight within a font file."""
        if not weight:
            return 0
        target_weight = weight.lower()
        index = 0
        while True:
            try:
                # Load font at the current index to check its name
                font = ImageFont.truetype(font_path, size=12, index=index)
                _family, style = font.getname()
                # Check for an exact or partial match in the style name
                if target_weight in style.lower().replace(" ", ""):
                    return index
                index += 1
            except (IOError, OSError):
                # This error means we've reached the end of available fonts in the file
                break
        return 0 # Fallback to the default index


    def find(self, name: str, weight: Optional[str] = None) -> Optional[Tuple[str, int]]:
        """
        Finds a font path and index by name and optional weight.
        Args:
            name: The name of the font family (e.g., "Helvetica", "Arial").
            weight: The desired weight (e.g., "Bold", "Light", "Regular").
        Returns:
            A tuple (font_path, font_index), or None if not found.
        """
        cache_key = (name.lower(), weight.lower() if weight else None)
        if cache_key in self._cache:
            return self._cache[cache_key]

        # Manual file search with index inspection (fallback)
        manual_path = self._find_font_manual(name, weight)
        if manual_path:
            # Now that we have a file, try to find the correct index within it
            font_index = self._get_font_index_from_file(manual_path, weight or "")
            result_tuple = (manual_path, font_index)
            self._cache[cache_key] = result_tuple
            return result_tuple

        self._cache[cache_key] = None
        return None


# --- Main Execution Logic ---
def main():
    """Main function to run the screenshot generator from the command line."""
    parser = argparse.ArgumentParser(description="Generate App Store style screenshots based on a YAML config.")
    parser.add_argument(
        '--config',
        type=str,
        default='config.yaml',
        help='Path to the YAML configuration file. Defaults to "config.yaml".'
    )
    parser.add_argument(
        '--name',
        type=str,
        default=None,
        help='The name of a single definition to generate from the config file. If omitted, all definitions are generated.'
    )
    args = parser.parse_args()

    print(f"Starting App Store Screenshot generation from '{args.config}'...")

    # Create output directory if it doesn't exist
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    # Load configurations from YAML
    definitions = load_config(args.config)
    if not definitions:
        sys.exit(1)

    # Filter for a specific definition if a name is provided
    if args.name:
        definitions = [d for d in definitions if d.get('name') == args.name]
        if not definitions:
            print(f"Error: Definition with name '{args.name}' not found in '{args.config}'.")
            sys.exit(1)

    # --- Font and File Handling ---
    font_manager = FontManager()
    processed_count = 0
    failed_count = 0
    skipped_text_count = 0

    for definition in definitions:
        name = definition.get('name', 'Unnamed Definition')
        print(f"\n--- Generating: {name} ---")

        # Check for required top-level keys
        background_config = definition.get('background')
        output_path = definition.get('output')
        elements = definition.get('elements')
        if not all([background_config, output_path, elements]):
            print(f"!!! SKIPPING '{name}': Missing 'background', 'output', or 'elements' key.")
            failed_count += 1
            continue

        # --- Pre-flight checks for files and fonts ---
        required_files = []
        if isinstance(background_config, str):
            required_files.append(background_config)

        font_issue_in_def = False
        for el in elements:
            if el.get('type', TYPE_IMAGE) == TYPE_IMAGE and el.get('image'):
                required_files.append(el['image'])
            
            elif el.get('type') == TYPE_TEXT:
                font_name = el.get('font_name')
                font_path = el.get('font_path')
                font_weight = el.get('font_weight')

                # If font_name is provided, resolve it to a path and index.
                if font_name:
                    resolved_font_info = font_manager.find(font_name, font_weight)
                    if resolved_font_info:
                        el['font_path'] = resolved_font_info[0]
                        el['font_index'] = resolved_font_info[1]
                        print(f"Resolved font info: {el['font_path']}, {el['font_index']}")
                    else:
                        print(f"!!! WARNING for '{name}': Font '{font_name}' with weight '{font_weight if font_weight else 'any'}' not found. This text element will be skipped.")
                        font_issue_in_def = True
                
                # If no name or path, try to find a default.
                elif not font_path:
                    # Try to find a common default font.
                    default_font_info = font_manager.find("Arial") or font_manager.find("Helvetica") or font_manager.find("DejaVu Sans")
                    if default_font_info:
                        el['font_path'] = default_font_info[0]
                        el['font_index'] = default_font_info[1]
                    else:
                        print(f"!!! WARNING for '{name}': No 'font_name' or 'font_path' provided, and could not find a default font. This text element will be skipped.")
                        font_issue_in_def = True

        if font_issue_in_def:
            skipped_text_count += 1

        missing_files = [f for f in required_files if f and not os.path.exists(f)]
        if missing_files:
            print(f"!!! SKIPPING '{name}': Missing required image files:")
            for f in missing_files: print(f"  - {f}")
            failed_count += 1
            continue
        
        # --- Generation ---
        try:
            generator = None
            if isinstance(background_config, str):
                generator = ScreenshotGenerator(background_source=background_config)
            elif isinstance(background_config, dict):
                generator = ScreenshotGenerator(
                    background_source=None,
                    size=background_config.get('size'),
                    color=background_config.get('color'),
                    gradient=background_config.get('gradient')
                )
            else:
                raise ValueError("Invalid 'background' configuration. Must be a file path string or a dictionary.")

            result = generator.generate(
                elements_config=list(elements), # Pass a copy
                output_path=output_path
            )

            output_exists = os.path.exists(output_path)
            if result is None and output_exists:
                processed_count += 1
                print(f"--- Generation successful for '{name}'. ---")
            else:
                failed_count += 1
                print(f"!!! Generation failed for '{name}'. Check logs above. !!!")

        except ValueError as e:
            print(f"!!! Error initializing generator for {name}: {e} !!!")
            failed_count += 1
        except Exception as e:
            print(f"!!! Unexpected Error during generation for {name}: {e} !!!")
            failed_count += 1

    print("\n--- App Store Screenshot Generation Summary ---")
    print(f"Successfully generated: {processed_count}")
    print(f"Failed definitions:     {failed_count}")
    if skipped_text_count > 0:
        print(f"Definitions with font issues: {skipped_text_count}")
    total_definitions = len(definitions) if not args.name else 1
    print(f"Total definitions attempted: {total_definitions}")
    if processed_count > 0:
        print(f"\nGenerated files are in the '{os.path.abspath(OUTPUT_DIR)}' directory.")
    if failed_count > 0:
         print("\nPlease check warnings or errors printed above for details.")
    print("---------------------------------------------")


if __name__ == "__main__":
    main()
