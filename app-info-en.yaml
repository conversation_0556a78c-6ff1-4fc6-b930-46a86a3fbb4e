### Name
LavaChat: Your AI Hub

### Subtitle 
Requirement: A summary of your app that will appear under your app's name on your App Store product page. This can't be longer than 30 characters.
Content: >
  Your Custom Multi-AI Chat


### Promotional Text
Requirement: Promotional text lets you inform your App Store visitors of any current app features without requiring an updated submission. This text will appear above your description on the App Store for customers. This can't be longer than 170 characters.
Content: >
  Experience a new way to chat with multiple AIs at once! LavaChat's unique stacked-card UI, powerful prompt Actions, and deep customization put you in control.

### Description
Requirement: A description of your app, detailing features and functionality. This can't be longer than 4000 characters.
Content: >
  Welcome to LavaChat, where you command the conversation. We've reimagined the AI chat experience from the ground up, giving you unprecedented control, customization, and power.

  【A Revolutionary Way to Chat】
  • Engage with multiple AI models from different providers simultaneously in our innovative stacked-card interface.
  • Send a prompt and receive answers from all selected AIs at once. Swipe through responses like a deck of cards, compare results, and choose the best one to continue the conversation.
  • Freely edit, regenerate, and branch your conversations to explore different lines of thought without losing context.

  【Unprecedented Customization】
  • Go beyond presets. Create and manage your own AI "Instances" by fine-tuning the full range of parameters supported by the model's API—from core settings like system prompts and temperature to advanced, model-specific options.
  • Build reusable "Chat Settings" that define every aspect of a conversation's behavior, including UI elements, context length, and which actions are available. Switch between settings like "Creative Writing" and "Technical Debugging" in an instant.

  【Powerful Automation with AI Actions】
  • Create custom prompts to transform any AI response. One tap can use AI to make a reply more concise, change its tone, translate it, or reformat it based on any prompt you design.
  • Supercharge your input. Build a library of your favorite prompt snippets to insert instantly, or create actions that use an AI to automatically optimize and enhance your own questions before you even send them.
  • Your workflow, your rules. Create, edit, and organize your actions. Decide exactly where they appear—on message cards, in context menus, or in the action panel—for a truly personalized experience.

  【Seamless Management & Collaboration】
  • Manage models from all major providers and add your own custom endpoints. Securely store your API keys in the keychain.
  • Group your custom AI Instances together for specialized tasks.
  • Share everything: Export and import your favorite Chat Sessions, AI Instances, or even your complete Chat Settings via file, iCloud link, or a simple QR code.

  LavaChat is more than a chat app; it's a central hub for you to orchestrate AI. Download now and take control of your AI-powered world.

### Keywords
Requirement: Include one or more keywords that describe your app. Keywords make App Store search results more accurate. Separate keywords with an English comma, Chinese comma, or a mix of both. This can't be longer than 100 characters.
Content: >
  AI,Chat,LLM,Multi-AI,Prompt,ChatGPT,Gemini,Claude,DeepSeek,OpenAI,Grok,Qwen,Anthropic,GPT,OpenRouter