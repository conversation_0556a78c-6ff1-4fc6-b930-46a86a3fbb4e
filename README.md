# App Store Screenshot Generator

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

[中文文档](README.zh-CN.md) | [English](README.md)

A simple Python script using Pillow to create app store-style screenshots by placing framed device mockups onto a background image.

<img src="output/class_example_03_triple_shadow_angled.png?v=2" alt="Example Output" height="400">
*(Example generated using the script)*

## Features

*   **Configuration via YAML:** Easily define all your screenshots in a single `config.yaml` file.
*   **Place multiple elements:** Add multiple screenshots and text elements to a background.
*   **Flexible Styling:** Customize frames with borders, shadows, or a frosted glass effect.
*   **Relative Positioning & Sizing:** Elements are positioned and sized relative to the background for easy adjustments.
*   **Stacking Order:** Control element overlap with `z_order`.
*   **Command-Line Control:** Generate all screenshots or target a specific one by name.

## Prerequisites

*   Python 3.6+
*   Pillow & PyYAML

## Installation

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/app-store-screenshot-generator.git
    cd app-store-screenshot-generator
    ```

2.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

## Usage

1.  **Prepare your images:**
    *   Place your background image (e.g., `background.jpeg`) and screenshot images (e.g., `screenshot1.png`) in the project directory.

2.  **Configure `config.yaml`:**
    *   Open the `config.yaml` file.
    *   Define one or more "definitions". Each definition represents a final screenshot to be generated.
    *   See the **Configuration Structure** section below for details on all available parameters.

3.  **Run the script:**

    *   **To generate all screenshots** defined in `config.yaml`:
        ```bash
        python app_store_screenshot_generator.py
        ```

    *   **To generate a single, specific screenshot** from the config, use the `--name` argument:
        ```bash
        python app_store_screenshot_generator.py --name "example_5_mixed_styles_text"
        ```

    *   **To use a different config file**:
        ```bash
        python app_store_screenshot_generator.py --config my_other_config.yaml
        ```

    Output images are saved to the `output/` directory.

## Configuration Structure

The `config.yaml` file is a list of screenshot definitions. Each definition is a dictionary with the following keys:

*   `name`: (Required) A unique name for this screenshot definition (string).
*   `background`: (Required) Path to the background image file (string).
*   `output`: (Required) Path to save the final generated screenshot (string).
*   `elements`: (Required) A list of element dictionaries to be placed on the background.

### Element Configuration

Each item in the `elements` list can be an `image` or a `text` element.

#### Common Element Keys

*   `type`: (Optional) `image` or `text`. Defaults to `image`.
*   `relative_position`: (Required) A list `[rel_x, rel_y]` for the *center* of the element relative to the background (e.g., `[0.5, 0.5]` is the exact center).
*   `rotation_degrees`: (Optional) Angle to rotate the element. Defaults to `0`.
*   `z_order`: (Optional) Integer for stacking order (higher value is on top). Defaults to `0`.

#### `image` Element Keys

*   `image`: (Required) Path to the screenshot file (string).
*   `relative_width`: (Required) Desired element width as a fraction of the background image width (e.g., `0.8` for 80%).
*   `frame_style`: (Optional) Style of the frame. Can be `black_border`, `frosted_glass`, `shadow`, or `none`. Defaults to `black_border`.
*   `relative_border_width`: (Optional) Border width as a fraction of the element's width. Used for `black_border` and `frosted_glass` styles, and determines the blur radius for the `shadow` style. Defaults to `0.02`.
*   `relative_corner_radius`: (Optional) Corner radius as a fraction of the element's width. Defaults to `0.2`.

#### `text` Element Keys

*   `text`: (Required) The text to display (string).
*   `font_path`: (Optional) Path to a `.ttf` or `.otf` font file. If omitted, the script will attempt to find a suitable system font.
*   `font_size`: (Required) The font size in points.
*   `color`: (Optional) A list of RGBA values `[R, G, B, A]` for the text color (e.g., `[255, 255, 255, 230]`). Defaults to black.

### Example `config.yaml`

```yaml
- name: "example_5_mixed_styles_text"
  background: "background.jpeg"
  output: "output/class_example_05_mixed_styles_text.png"
  elements:
    - type: "image"
      image: "screenshot1.png"
      relative_width: 0.45
      relative_position: [0.3, 0.65]
      frame_style: "shadow"
      relative_border_width: 0.06
      relative_corner_radius: 0.2
      z_order: 0
      rotation_degrees: -5
    - type: "text"
      text: "Your Awesome App!"
      relative_position: [0.5, 0.15]
      font_size: 90
      color: [255, 255, 255, 230]
      rotation_degrees: -3
      z_order: 2
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
