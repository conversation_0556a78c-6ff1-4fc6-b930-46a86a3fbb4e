# English
# image 1: "Welcome to LavaChat" page, and show swipe right gesture of multi AI chat
# text: 欢迎使用"熔言"，\n您的多AI中枢
- name: "lavachat_sc_1"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_sc_1.png"
  elements:
    - type: "text"
      text: "欢迎使用      ，"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Regular"
      z_order: 1
    - type: "text"
      text: "熔言"
      relative_position: [0.582, 0.058]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "您的多AI中枢"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Regular"
      z_order: 1
    - type: "image"
      image: "lavachat/sc-1.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 2: Multi AI configuration sheet, user can add, remove, and turn thinking/searching on/off for each individual AI agents
# text: 配置你的 "AIs"，\n打造专属体验
- name: "lavachat_sc_2"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_sc_2.png"
  elements:
    - type: "text"
      text: "配置你的      ,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "AIs"
      relative_position: [0.705, 0.058]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "打造专属体验"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/sc-2.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 3: Action prompt selection sheet, highlighting powerful AI-powered prompt enhancement and transformation tools.
# text: 自定义 "AI指令"，\n彻底改变聊天方式
- name: "lavachat_sc_3"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_sc_3.png"
  elements:
    - type: "text"
      text: "自定义                  ,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "AI指令"
      relative_position: [0.626, 0.058]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "彻底改变聊天方式"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/sc-3b.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 4: "Instance Details" page, illustrating fine-grained control over system prompt and custom AI instance parameters.
# text: 你的AI，你做主，\n深度"自定义"选项
- name: "lavachat_sc_4"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_sc_4.png"
  elements:
    - type: "text"
      text: "您的AI，您做主，"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "深度        选项"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "自定义"
      relative_position: [0.458, 0.0925]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "image"
      image: "lavachat/sc-4.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 5: Chat with image as input, text could mention that app have both image and file input support.
# text: "图片 & 文件",\n沟通更多彩
- name: "lavachat_sc_5"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_sc_5.png"
  elements:
    - type: "text"
      text: "图片 & 文件"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "沟通更多彩"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/sc-5.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 6: "Models" tab, showcasing diverse AI providers, models and the ability to manage custom AI instances.
# text: 连接 "任意" 提供商，\n任意模型，任意实例
- name: "lavachat_sc_6"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_sc_6.png"
  elements:
    - type: "text"
      text: "连接        提供商，"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "任意"
      relative_position: [0.533, 0.0604]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "任意模型，任意实例"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/sc-6.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 7: "Chat Settings" page, demonstrating comprehensive customization options for chat behavior and UI.
# text: 自定义 "聊天设置"，\n体验随心调整
- name: "lavachat_sc_7"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_sc_7.png"
  elements:
    - type: "text"
      text: "自定义                       ,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "聊天设置"
      relative_position: [0.627, 0.0598]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "体验随心调整"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/sc-7.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 8: Share sheet, showing the ability to share chat history, settings, and more.
# text: 便捷 "分享",\n连接你我，高效协作
- name: "lavachat_sc_8"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_sc_8.png"
  elements:
    - type: "text"
      text: "便捷              ,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 2
    - type: "text"
      text: "分享"
      relative_position: [0.657, 0.0598]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "连接你我，高效协作"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "PingFang SC"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/sc-8.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0