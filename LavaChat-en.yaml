# English
# image 1: "Welcome to LavaChat" page, and show swipe right gesture of multi AI chat
# text: Meet "LavaChat",\nYour Multi-AI Hub
- name: "lavachat_en_1"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_en_1.png"
  elements:
    - type: "text"
      text: "Meet                 ,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "LavaChat"
      relative_position: [0.582, 0.058]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "Your Multi-AI Hub"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/en-1.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 2: Multi AI configuration sheet, user can add, remove, and turn thinking/searching on/off for each individual AI agents
# text: Control Your "AIs",\nShape Your Experience
- name: "lavachat_en_2"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_en_2.png"
  elements:
    - type: "text"
      text: "Control Your      ,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "AIs"
      relative_position: [0.705, 0.058]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "Shape Your Experience"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/en-2.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 3: Action prompt selection sheet, highlighting powerful AI-powered prompt enhancement and transformation tools.
# text: Custom "AI Actions",\nTransform Your Chat
- name: "lavachat_en_3"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_en_3.png"
  elements:
    - type: "text"
      text: "Custom                  ,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "AI Actions"
      relative_position: [0.626, 0.058]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "Transform Your Chat"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/en-3b.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 4: "Instance Details" page, illustrating fine-grained control over system prompt and custom AI instance parameters.
# text: Your AI, Your Rules,\nDeep "Custom" Options
- name: "lavachat_en_4"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_en_4.png"
  elements:
    - type: "text"
      text: "Your AI, Your Rules,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "Deep               Options"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "Custom"
      relative_position: [0.458, 0.0925]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "image"
      image: "lavachat/en-4.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 5: Chat with image as input, text could mention that app have both image and file input support.
# text: "Image & File",\nFor Richer Chats
- name: "lavachat_en_5"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_en_5.png"
  elements:
    - type: "text"
      text: "Image & File"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "For Richer Chats"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/en-5.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 6: "Models" tab, showcasing diverse AI providers, models and the ability to manage custom AI instances.
# text: Connect to "Any" Provider,\nAny Model, Any Instance
- name: "lavachat_en_6"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_en_6.png"
  elements:
    - type: "text"
      text: "Connect to        Provider,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "Any"
      relative_position: [0.533, 0.0604]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "Any Model, Any Instance"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/en-6.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 7: "Chat Settings" page, demonstrating comprehensive customization options for chat behavior and UI.
# text: Custom "Chat Settings",\nTailor Your Experience
- name: "lavachat_en_7"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_en_7.png"
  elements:
    - type: "text"
      text: "Custom                       ,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "text"
      text: "Chat Settings"
      relative_position: [0.627, 0.0598]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "Tailor Your Experience"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/en-7.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0

# image 8: Share sheet, showing the ability to share chat history, settings, and more.
# text: Seamless "Sharing",\nConnect and Collaborate
- name: "lavachat_en_8"
  background:
    size: [1284, 2778]
    color: "#FFFFFF"
  output: "output/lavachat_en_8.png"
  elements:
    - type: "text"
      text: "Seamless              ,"
      relative_position: [0.5, 0.06]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 2
    - type: "text"
      text: "Sharing"
      relative_position: [0.657, 0.0598]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      gradient:
        direction: "horizontal"
        colors: ["#FFD93D", "#FD5E53"]
      z_order: 2
    - type: "text"
      text: "Connect and Collaborate"
      relative_position: [0.5, 0.095]
      font_size: 90
      font_name: "Helvetica Neue"
      font_weight: "Medium"
      z_order: 1
    - type: "image"
      image: "lavachat/en-8.png"
      relative_width: 0.81
      relative_position: [0.5, 0.57]
      frame_style: "black_border"
      relative_border_width: 0.015
      relative_corner_radius: 0.17
      z_order: 0