#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
App Store Screenshot Generator
==============================

This script creates app store-style screenshots by placing framed device mockups
containing your app screenshots onto a background image using relative positioning
and sizing.

This version uses the ScreenshotGenerator class from app_store_screenshot_generator.py.

See README.md for usage instructions.
"""
import os
from typing import List, Dict
import sys

app_store_screenshot_generator.py# 导入 ScreenshotGenerator 类
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from app_store_screenshot_generator import (
    ScreenshotGenerator, 
    TYPE_IMAGE, 
    STYLE_BLACK_BORDER,
    OUTPUT_DIR
)

# 确保输出目录存在
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# --- 定义 Mockup 配置 ---
# 现在使用 ScreenshotGenerator 所需的配置格式
MOCKUP_DEFINITIONS = {
    "IMG_3851": {
        "background": "IMG_3851.jpg",  # 将在代码中检查多种扩展名
        "config": [
            {
                "type": TYPE_IMAGE,
                "image": "image1.png",
                "relative_width": 0.8,
                "relative_position": (0.5, 0.57),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 0
            }
        ]
    },
    "IMG_3906": {
        "background": "IMG_3906.jpg",
        "config": [
            {
                "type": TYPE_IMAGE,
                "image": "image1.png",
                "relative_width": 0.45,
                "relative_position": (0.25, 0.3),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 0
            },
            {
                "type": TYPE_IMAGE,
                "image": "image7.png",
                "relative_width": 0.45,
                "relative_position": (0.75, 0.3),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 1
            }
        ]
    },
    "IMG_3852": {
        "background": "IMG_3852.jpg",
        "config": [
            # 层级 1 (底层)
            {
                "type": TYPE_IMAGE,
                "image": "image2.png",
                "relative_width": 0.54,
                "relative_position": (0.34, 0.47),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 0
            },
            # 层级 2 (中层)
            {
                "type": TYPE_IMAGE,
                "image": "image4.png",
                "relative_width": 0.54,
                "relative_position": (0.5, 0.55),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 1
            },
            # 层级 3 (顶层)
            {
                "type": TYPE_IMAGE,
                "image": "image3.png",
                "relative_width": 0.54,
                "relative_position": (0.66, 0.63),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 2
            },
        ]
    },
    "IMG_3853": {
        "background": "IMG_3853.jpg",
        "config": [
            {
                "type": TYPE_IMAGE,
                "image": "image7.png",
                "relative_width": 0.8,
                "relative_position": (0.5, 0.56),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 0
            }
        ]
    },
    "IMG_3854": {
        "background": "IMG_3854.jpg",
        "config": [
            {
                "type": TYPE_IMAGE,
                "image": "image8.png",
                "relative_width": 0.8,
                "relative_position": (0.5, 0.56),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 0
            }
        ]
    },
    "IMG_3855": {
        "background": "IMG_3855.jpg",
        "config": [
            {
                "type": TYPE_IMAGE,
                "image": "image9.png",
                "relative_width": 0.8,
                "relative_position": (0.5, 0.56),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 0
            }
        ]
    },
    "IMG_3856": {
        "background": "IMG_3856.jpg",
        "config": [
            {
                "type": TYPE_IMAGE,
                "image": "image10.png",
                "relative_width": 0.8,
                "relative_position": (0.5, 0.56),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 0
            }
        ]
    },
    "IMG_3857": {
        "background": "IMG_3857.jpg",
        "config": [
            {
                "type": TYPE_IMAGE,
                "image": "image11.png",
                "relative_width": 0.8,
                "relative_position": (0.5, 0.56),
                "frame_style": STYLE_BLACK_BORDER,
                "z_order": 0
            }
        ]
    }
    # 可以在此处添加其他配置...
}

def find_background_file(bg_key):
    """查找背景图片文件，尝试多种扩展名"""
    for ext in [".jpg", ".jpeg", ".png", ".webp"]:
        potential_path = f"{bg_key}{ext}"
        if os.path.exists(potential_path):
            return potential_path
    return None

def check_screenshot_files(configs):
    """检查所有截图文件是否存在"""
    for cfg in configs:
        img_src = cfg.get("image")
        if isinstance(img_src, str) and not os.path.exists(img_src):
            return False
    return True

if __name__ == "__main__":
    print("开始生成 App Store 风格截图...")
    
    # 处理每个定义的 mockup
    processed_count = 0
    failed_count = 0
    
    for mockup_name, definition in MOCKUP_DEFINITIONS.items():
        print(f"\n--- 正在生成: {mockup_name} ---")
        
        # 准备背景图片路径
        bg_key = mockup_name
        bg_path = find_background_file(bg_key)
        
        if not bg_path:
            print(f"警告: 未找到背景图片 '{bg_key}' (尝试了 .jpg, .jpeg, .png, .webp)。跳过。")
            failed_count += 1
            continue
            
        # 更新定义中的背景路径
        definition["background"] = bg_path
        
        # 检查截图文件是否存在
        if not check_screenshot_files(definition["config"]):
            print(f"警告: 背景 '{bg_key}' 的一些截图文件未找到。跳过此 mockup。")
            failed_count += 1
            continue
            
        # 设置输出路径
        output_path = os.path.join(OUTPUT_DIR, f"{mockup_name}_output.png")
        
        try:
            # 创建 ScreenshotGenerator 实例
            generator = ScreenshotGenerator(definition["background"])
            
            # 生成截图
            result = generator.generate(
                elements_config=definition["config"],
                output_path=output_path
            )
            
            # 检查结果
            if os.path.exists(output_path):
                processed_count += 1
                print(f"--- '{mockup_name}' 生成成功 ---")
            else:
                failed_count += 1
                print(f"!!! 错误: '{mockup_name}' 生成失败，未创建输出文件。请检查之前的错误。!!!")
                
        except Exception as e:
            print(f"!!! 生成 '{mockup_name}' 时出现错误: {e} !!!")
            failed_count += 1
    
    # 打印总结
    print("\n--- App Store 截图生成摘要 ---")
    print(f"成功生成: {processed_count}")
    print(f"失败: {failed_count}")
    total = len(MOCKUP_DEFINITIONS)
    print(f"总计尝试: {total}")
    
    if processed_count > 0:
        print(f"\n生成的截图保存在 '{os.path.abspath(OUTPUT_DIR)}' 目录中。")
    if failed_count > 0:
        print("\n请检查上面打印的警告或错误，了解失败的详细信息。")
    print("---------------------------------------------")