### Name
Content: 熔言-你的AI中枢

### Subtitle 
Requirement: A summary of your app that will appear under your app's name on your App Store product page. This can't be longer than 30 characters.
Content: >
  多AI同时对话，创新AI指令，深度定制专属AI对话体验


### Promotional Text
Requirement: Promotional text lets you inform your App Store visitors of any current app features without requiring an updated submission. This text will appear above your description on the App Store for customers. This can't be longer than 170 characters.
Content: >
  熔言为你带来革命性的多AI对话体验！独创堆叠卡片式交互，让模型对比一目了然。创新的快捷AI指令与深度定制化能力，让你轻松打造专属AI助手。立即下载，开启AI对话新纪元！

### Description
Requirement: A description of your app, detailing features and functionality. This can't be longer than 4000 characters.
Content: >
  欢迎来到熔言（LavaChat），一个专为探索AI可能性而生的全新世界。我们从零开始，为你重新构想了与AI的交互方式，带来前所未有的自由度、创造力与乐趣。

  【革命性的对话体验】
  • 创新的堆叠卡片式界面，让您能够同时与来自不同提供商的多个AI模型进行对话。
  • 发送一个指令，即可同时获得所有选定AI的回复。像浏览卡片一样轻松滑动，直观地比较不同模型的答案，并选择最满意的一个继续对话。
  • 自由地编辑、重新生成和创建对话分支，尽情探索不同的思路，而无需担心丢失上下文。

  【释放无限创造力】
  • 超越预设的局限。通过创建和管理您自己的AI“实例”，您可以调整模型API支持的全部参数——无论是系统提示词、温度等核心设置，还是其它任何高级或模型专属的选项，皆可完全掌控。
  • 构建可复用的“聊天设置”，定义对话的方方面面，包括UI元素、上下文长度和可用的快捷指令。一键即可在“创意写作”和“技术问答”等不同场景间切换。

  【强大的自动化快捷AI指令】
  • 创建自定义指令来一键处理AI的回复。无论是要求回复更简洁、改变语气、翻译，还是根据您设计的任何提示词重新格式化，都轻而易舉。
  • 强化您的输入。建立您最喜欢的提示词片段库以便即时插入，或创建能让AI在发送问题前自动优化和增强您提问的指令。
  • 您的工作流，您做主。自由创建、编辑和组织您的快捷指令，并决定它们出现在消息卡片、上下文菜单还是操作面板中，享受真正个性化的体验。

  【无缝管理与分享】
  • 统一管理来自各大主流提供商的模型，并支持添加您自己的自定义模型接入点。您的API密钥将安全地存储在钥匙串中。
  • 将您的自定义AI实例分组，以应对特定的任务场景。
  • 分享一切：通过文件、iCloud链接或简单的二维码，轻松导出和导入您最喜欢的聊天会话、AI实例，甚至是完整的聊天设置。

  熔言不仅是一个聊天应用；它是一个让您指挥和编排AI的智能中枢。立即下载，开启您的AI探索之旅。

### Keywords
Requirement: Include one or more keywords that describe your app. Keywords make App Store search results more accurate. Separate keywords with an English comma, Chinese comma, or a mix of both. This can't be longer than 100 characters.
Content: >
  AI,聊天,多模型,LLM,提示词,工作流,自动化,DeepSeek,Qwen,Kimi,GLM,深度求索,通义千问,硅基流动,ZAI,多模态,大模型,chatbot